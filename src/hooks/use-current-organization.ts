import { usePathname } from "next/navigation";

export function useCurrentOrganization() {
  const pathname = usePathname();
  
  // Extract organization ID from URL pattern: /dashboard/[orgId]/...
  const pathSegments = pathname.split('/');
  
  if (pathSegments.length >= 3 && pathSegments[1] === 'dashboard') {
    return pathSegments[2]; // This is the orgId
  }
  
  // Default to 'personal' if no organization in URL
  return 'personal';
}
